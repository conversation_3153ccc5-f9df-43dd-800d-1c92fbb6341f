#!/usr/bin/env python
"""
Test script to verify the timeline functionality works correctly.
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from queue_system.models import QueuedJob, JobTimeline
from orders.models import order as Order
from locations.models import Location
from django.contrib.auth.models import User

def test_timeline_functionality():
    """Test the timeline functionality with a sample job"""
    print("🧪 Testing Timeline Functionality")
    print("=" * 50)
    
    try:
        # Get or create a test order and location
        location, created = Location.objects.get_or_create(
            location_name="Test Location",
            defaults={'description': 'Test location for timeline testing'}
        )
        
        import time
        unique_email = f"test_timeline_{int(time.time())}@example.com"
        order, created = Order.objects.get_or_create(
            customer_email=unique_email,
            defaults={
                'first_name': "Test",
                'surname': "User"
            }
        )
        
        # Create a test job
        job = QueuedJob.objects.create(
            order=order,
            location=location,
            status='queued'
        )
        
        print(f"✅ Created test job: {job.id}")
        
        # Check if timeline events were created automatically
        timeline_events = job.timeline_events.all()
        print(f"📋 Timeline events created: {timeline_events.count()}")
        
        for event in timeline_events:
            print(f"   {event.get_event_icon()} {event.timestamp.strftime('%H:%M:%S')} - {event.description}")
        
        # Test status changes
        print("\n🔄 Testing status changes...")
        
        # Mark as processing
        job.mark_as_processing(worker_id="test_worker_123")
        print(f"   ▶️ Marked as processing")
        
        # Mark as completed
        job.mark_as_completed()
        print(f"   ✅ Marked as completed")
        
        # Check timeline events again
        timeline_events = job.timeline_events.all()
        print(f"\n📋 Total timeline events: {timeline_events.count()}")
        
        print("\n📅 Complete Timeline:")
        for event in timeline_events:
            print(f"   {event.get_event_icon()} {event.timestamp.strftime('%H:%M:%S')} - {event.description}")
            if event.previous_status and event.new_status:
                print(f"      Status: {event.previous_status} → {event.new_status}")
            if event.metadata:
                print(f"      Metadata: {list(event.metadata.keys())}")
        
        # Test failure and retry
        print("\n🔄 Testing failure and retry...")
        
        # Create another job for failure testing
        job2 = QueuedJob.objects.create(
            order=order,
            location=location,
            status='queued'
        )
        
        job2.mark_as_processing(worker_id="test_worker_456")
        job2.mark_as_failed(
            error_message="Test error message",
            failure_reason="test_failure",
            error_details={"test": "data"}
        )
        
        print(f"   ❌ Job {job2.id} marked as failed")
        
        # Test requeue
        admin_user, created = User.objects.get_or_create(
            username="test_admin",
            defaults={'email': '<EMAIL>'}
        )
        
        job2.requeue_job(
            admin_user=admin_user,
            priority=True,
            reason="Testing requeue functionality"
        )
        
        print(f"   🔁 Job {job2.id} requeued with priority")
        
        # Show timeline for failed/requeued job
        timeline_events2 = job2.timeline_events.all()
        print(f"\n📋 Timeline for job {job2.id} ({timeline_events2.count()} events):")
        
        for event in timeline_events2:
            print(f"   {event.get_event_icon()} {event.timestamp.strftime('%H:%M:%S')} - {event.description}")
            if event.user:
                print(f"      By: {event.user.username}")
            if event.previous_status and event.new_status:
                print(f"      Status: {event.previous_status} → {event.new_status}")
        
        print("\n🎉 Timeline functionality test completed successfully!")
        print(f"✅ Created {timeline_events.count() + timeline_events2.count()} timeline events total")
        
        # Cleanup
        print("\n🧹 Cleaning up test data...")
        job.delete()
        job2.delete()
        if created:
            order.delete()
        print("✅ Cleanup completed")
        
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()

def test_timeline_display():
    """Test timeline display methods"""
    print("\n🎨 Testing Timeline Display Methods")
    print("=" * 50)
    
    # Test all event types
    event_types = [
        'created', 'queued', 'processing_started', 'processing_completed',
        'failed', 'retry_attempted', 'moved_to_review', 'reviewed',
        'requeued', 'cancelled', 'priority_changed', 'worker_assigned',
        'worker_released', 'error_occurred', 'status_changed', 'manual_intervention'
    ]
    
    print("Event Type Icons and Colors:")
    for event_type in event_types:
        # Create a dummy timeline event to test display methods
        from queue_system.models import JobTimeline
        dummy_event = JobTimeline(event_type=event_type)
        icon = dummy_event.get_event_icon()
        color = dummy_event.get_event_color()
        display = dummy_event.get_event_type_display()
        
        print(f"   {icon} {event_type:<20} {color:<20} {display}")
    
    print("\n✅ Display methods test completed!")

if __name__ == "__main__":
    test_timeline_functionality()
    test_timeline_display()
    print("\n🏁 All tests completed!")
