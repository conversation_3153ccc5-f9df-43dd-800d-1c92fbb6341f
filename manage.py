#!/usr/bin/env python
"""Django's command-line utility for administrative tasks."""
import os
import sys
import subprocess


def main():
    """Run administrative tasks."""
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "config.settings")
    try:
        from django.core.management import execute_from_command_line
    except ImportError as exc:
        raise ImportError(
            "Couldn't import Django. Are you sure it's installed and "
            "available on your PYTHONPATH environment variable? Did you "
            "forget to activate a virtual environment?"
        ) from exc
    execute_from_command_line(sys.argv)


if __name__ == "__main__":
    if "runserver" in sys.argv:
        # Start Celery Beat
        subprocess.Popen([
            "celery", "-A", "config", "beat", "--loglevel=info"
        ])
        
        # Start Celery Worker
        subprocess.Popen([
            "celery", "-A", "config", "worker", "--loglevel=info",
            "--concurrency=1", "--queues=scheduler",
            "--hostname=scheduler_worker@%h", "--pool=solo"
        ])
    main()
