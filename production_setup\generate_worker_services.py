#!/usr/bin/env python3
"""
Generate systemd service files for all locations
Run this script to create production-ready systemd services
"""

import os
import sys
import django

# Setup Django
sys.path.append('/home/<USER>/JG-Innovations--master')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from locations.models import Location

def generate_services():
    """Generate systemd service files for all locations"""
    
    # Read template
    template_path = '/home/<USER>/JG-Innovations--master/production_setup/celery-worker-template.service'
    
    try:
        with open(template_path, 'r') as f:
            template = f.read()
    except FileNotFoundError:
        print(f"❌ Template file not found: {template_path}")
        return

    # Get all locations
    locations = Location.objects.all()
    
    if not locations:
        print("❌ No locations found in database")
        return

    print(f"🏭 Generating systemd services for {locations.count()} locations...")
    
    services_dir = '/home/<USER>/JG-Innovations--master/production_setup/services'
    os.makedirs(services_dir, exist_ok=True)
    
    generated_files = []
    
    for location in locations:
        # Clean location name for filename
        clean_name = location.location_name.lower().replace(' ', '_').replace('-', '_')
        clean_name = ''.join(c for c in clean_name if c.isalnum() or c == '_')
        
        # Replace placeholders
        service_content = template.replace('LOCATION_ID', str(location.id))
        service_content = service_content.replace('LOCATION_NAME', clean_name)
        
        # Write service file
        service_filename = f'celery-worker-{clean_name}.service'
        service_path = os.path.join(services_dir, service_filename)
        
        with open(service_path, 'w') as f:
            f.write(service_content)
        
        generated_files.append({
            'location': location.location_name,
            'file': service_filename,
            'path': service_path,
            'queue': f'location.{location.id}'
        })
        
        print(f"✅ Generated: {service_filename} for {location.location_name}")

    # Generate installation script
    install_script_path = os.path.join(services_dir, 'install_services.sh')
    
    with open(install_script_path, 'w') as f:
        f.write("#!/bin/bash\n")
        f.write("# Auto-generated systemd service installation script\n")
        f.write("# Run with sudo: sudo bash install_services.sh\n\n")
        
        f.write("echo '🚀 Installing Celery worker services...'\n\n")
        
        # Create directories
        f.write("# Create required directories\n")
        f.write("mkdir -p /var/run/celery /var/log/celery\n")
        f.write("chown jgmadlga:jgmadlga /var/run/celery /var/log/celery\n")
        f.write("chmod 755 /var/run/celery /var/log/celery\n\n")
        
        # Install services
        f.write("# Install service files\n")
        for service in generated_files:
            f.write(f"cp {service['file']} /etc/systemd/system/\n")
        
        f.write("\n# Reload systemd\n")
        f.write("systemctl daemon-reload\n\n")
        
        # Enable services
        f.write("# Enable services\n")
        for service in generated_files:
            service_name = service['file']
            f.write(f"systemctl enable {service_name}\n")
        
        f.write("\n# Start services\n")
        for service in generated_files:
            service_name = service['file']
            f.write(f"systemctl start {service_name}\n")
        
        f.write("\necho '✅ All services installed and started'\n")
        f.write("echo 'Check status with: systemctl status celery-worker-*'\n")

    # Make install script executable
    os.chmod(install_script_path, 0o755)
    
    # Generate status check script
    status_script_path = os.path.join(services_dir, 'check_workers.sh')
    
    with open(status_script_path, 'w') as f:
        f.write("#!/bin/bash\n")
        f.write("# Check status of all Celery worker services\n\n")
        
        f.write("echo '📊 Celery Worker Status'\n")
        f.write("echo '========================'\n\n")
        
        for service in generated_files:
            service_name = service['file']
            f.write(f"echo '📍 {service['location']} ({service['queue']})'\n")
            f.write(f"systemctl is-active {service_name} --quiet && echo '  ✅ Running' || echo '  ❌ Stopped'\n")
            f.write(f"echo\n")
        
        f.write("\necho 'Detailed status:'\n")
        f.write("systemctl status celery-worker-* --no-pager -l\n")

    os.chmod(status_script_path, 0o755)

    # Summary
    print(f"\n📊 SUMMARY")
    print(f"Generated {len(generated_files)} service files in: {services_dir}")
    print(f"\n📋 NEXT STEPS:")
    print(f"1. Review generated files in: {services_dir}")
    print(f"2. Install services: sudo bash {install_script_path}")
    print(f"3. Check status: bash {status_script_path}")
    print(f"\n📁 Generated files:")
    for service in generated_files:
        print(f"  • {service['file']} → {service['location']}")
    
    print(f"\n🔧 Management scripts:")
    print(f"  • {install_script_path} - Install all services")
    print(f"  • {status_script_path} - Check worker status")

if __name__ == '__main__':
    generate_services()
