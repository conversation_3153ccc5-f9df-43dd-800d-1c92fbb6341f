# Generated manually on 2025-07-14
from django.db import migrations


class Migration(migrations.Migration):
    """
    This migration doesn't make any schema changes but serves as a marker for code changes
    that fix how the reviewed_by field is handled in the QueuedJob model methods.
    
    The fix ensures that User objects are properly converted to usernames before being
    stored in the reviewed_by CharField.
    """

    dependencies = [
        ('queue_system', '0003_jobtimeline'),
    ]

    operations = [
        # No schema changes, just a marker for code changes
        # This migration marks the fix for reviewed_by field handling in model methods
    ]
