#!/usr/bin/env python3
"""
Production Worker Management Command
Starts Celery workers for each location in a production-friendly way
"""

from django.core.management.base import BaseCommand
from django.conf import settings
from locations.models import Location
from queue_system.models import LocationQueueConfig
import subprocess
import os
import sys
import time

class Command(BaseCommand):
    help = 'Start production Celery workers for all locations'

    def add_arguments(self, parser):
        parser.add_argument(
            '--location-id',
            type=str,
            help='Start worker for specific location ID only'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show commands that would be executed without running them'
        )
        parser.add_argument(
            '--background',
            action='store_true',
            help='Start workers in background (daemon mode)'
        )

    def handle(self, *args, **options):
        self.stdout.write("🚀 Production Worker Management")
        self.stdout.write("=" * 50)

        # Get locations to process
        if options['location_id']:
            try:
                locations = [Location.objects.get(id=options['location_id'])]
                self.stdout.write(f"Processing specific location: {locations[0].location_name}")
            except Location.DoesNotExist:
                self.stdout.write(self.style.ERROR(f"Location with ID {options['location_id']} not found"))
                return
        else:
            locations = Location.objects.all()
            self.stdout.write(f"Processing all {locations.count()} locations")

        if not locations:
            self.stdout.write(self.style.WARNING("No locations found"))
            return

        # Check Redis connectivity
        self.stdout.write("\n🔍 Checking Redis connectivity...")
        if not self._check_redis():
            self.stdout.write(self.style.ERROR("❌ Redis is not available. Workers cannot start."))
            self.stdout.write("Please ensure Redis is running: redis-server")
            return

        self.stdout.write(self.style.SUCCESS("✅ Redis is available"))

        # Process each location
        started_workers = []
        failed_workers = []

        for location in locations:
            self.stdout.write(f"\n📍 Processing {location.location_name}...")
            
            # Get or create queue config
            config, created = LocationQueueConfig.objects.get_or_create(
                location=location,
                defaults={'max_workers': 1, 'active_workers': 0}
            )

            if created:
                self.stdout.write(f"   Created new queue config for {location.location_name}")

            # Determine how many workers to start
            workers_needed = max(0, config.max_workers - config.active_workers)
            
            if workers_needed == 0:
                self.stdout.write(f"   ✅ Already at capacity ({config.active_workers}/{config.max_workers})")
                continue

            self.stdout.write(f"   Starting {workers_needed} worker(s)...")

            # Start workers for this location
            for i in range(workers_needed):
                worker_num = config.active_workers + i + 1
                worker_name = f'prod_worker_{location.location_name}_{worker_num}'
                queue_name = f'location.{location.id}'

                # Build command
                cmd = [
                    'celery', '-A', 'config', 'worker',
                    '--loglevel=info',
                    '--concurrency=1',
                    f'--queues={queue_name}',
                    f'--hostname={worker_name}@%h',
                    '--without-gossip',
                    '--without-mingle',
                    '--without-heartbeat',
                ]

                if options['background']:
                    cmd.append('--detach')

                # Show command
                cmd_str = ' '.join(cmd)
                self.stdout.write(f"   Command: {cmd_str}")

                if options['dry_run']:
                    self.stdout.write(f"   [DRY RUN] Would start: {worker_name}")
                    continue

                # Execute command
                try:
                    if options['background']:
                        # Background mode
                        process = subprocess.Popen(
                            cmd,
                            stdout=subprocess.DEVNULL,
                            stderr=subprocess.DEVNULL,
                            stdin=subprocess.DEVNULL,
                            start_new_session=True,
                            preexec_fn=os.setsid if hasattr(os, 'setsid') else None
                        )
                        time.sleep(1)  # Brief wait
                        
                        if process.poll() is None:
                            started_workers.append({
                                'name': worker_name,
                                'location': location.location_name,
                                'queue': queue_name,
                                'pid': process.pid
                            })
                            self.stdout.write(f"   ✅ Started {worker_name} (PID: {process.pid})")
                        else:
                            failed_workers.append(worker_name)
                            self.stdout.write(f"   ❌ Failed to start {worker_name}")
                    else:
                        # Foreground mode - just show the command
                        self.stdout.write(f"   To start manually: {cmd_str}")
                        started_workers.append({
                            'name': worker_name,
                            'location': location.location_name,
                            'queue': queue_name,
                            'cmd': cmd_str
                        })

                except Exception as e:
                    failed_workers.append(worker_name)
                    self.stdout.write(f"   ❌ Error starting {worker_name}: {e}")

            # Update active worker count if workers were started
            if not options['dry_run'] and options['background']:
                successful_starts = len([w for w in started_workers if w['location'] == location.location_name])
                if successful_starts > 0:
                    config.active_workers += successful_starts
                    config.save()

        # Summary
        self.stdout.write("\n" + "=" * 50)
        self.stdout.write("📊 SUMMARY")
        
        if options['dry_run']:
            self.stdout.write(f"🔍 DRY RUN - No workers actually started")
        else:
            self.stdout.write(f"✅ Successfully started: {len(started_workers)} workers")
            self.stdout.write(f"❌ Failed to start: {len(failed_workers)} workers")

        if started_workers:
            self.stdout.write("\n✅ STARTED WORKERS:")
            for worker in started_workers:
                if 'pid' in worker:
                    self.stdout.write(f"   🔴 {worker['name']} (PID: {worker['pid']}) - Queue: {worker['queue']}")
                else:
                    self.stdout.write(f"   📋 {worker['name']} - Queue: {worker['queue']}")

        if failed_workers:
            self.stdout.write("\n❌ FAILED WORKERS:")
            for worker in failed_workers:
                self.stdout.write(f"   {worker}")

        # Instructions
        self.stdout.write("\n📋 PRODUCTION NOTES:")
        self.stdout.write("• For production, use a process manager like systemd or supervisor")
        self.stdout.write("• Workers started with --background will run as daemon processes")
        self.stdout.write("• Monitor workers with: celery -A config inspect active")
        self.stdout.write("• Stop all workers: pkill -f 'celery.*worker'")

    def _check_redis(self):
        """Check if Redis is available"""
        try:
            from config.celery import app as celery_app
            celery_app.control.inspect().stats()
            return True
        except Exception:
            return False
