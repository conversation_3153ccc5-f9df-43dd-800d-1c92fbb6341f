#!/usr/bin/env python3
"""
API Endpoint Testing Script
Tests all order-related API endpoints to ensure they are accessible
"""

import requests
import json
from datetime import datetime

# Configuration
BASE_URL = "https://jgmanage.business"  # Change to your domain
API_BASE = f"{BASE_URL}/api"

def test_endpoint(method, endpoint, data=None, expected_status=200):
    """Test a single API endpoint"""
    url = f"{API_BASE}{endpoint}"
    
    try:
        if method.upper() == 'GET':
            response = requests.get(url, timeout=10)
        elif method.upper() == 'POST':
            response = requests.post(url, json=data, timeout=10)
        elif method.upper() == 'PUT':
            response = requests.put(url, json=data, timeout=10)
        elif method.upper() == 'DELETE':
            response = requests.delete(url, timeout=10)
        else:
            return False, f"Unsupported method: {method}"
        
        success = response.status_code == expected_status
        return success, {
            'status_code': response.status_code,
            'response_size': len(response.content),
            'content_type': response.headers.get('content-type', 'unknown')
        }
    
    except requests.exceptions.RequestException as e:
        return False, f"Request failed: {str(e)}"

def main():
    """Test all order API endpoints"""
    
    print("🔍 Testing Order API Endpoints")
    print("=" * 50)
    
    # Test cases: (method, endpoint, data, expected_status)
    test_cases = [
        # Core Order APIs
        ('GET', '/orders/', None, 200),
        ('GET', '/locations/', None, 200),
        
        # Custom Order APIs
        ('GET', '/order-details', None, 200),
        ('POST', '/order-details', {'first_name': 'Test'}, 200),
        ('GET', '/total-orders', None, 200),
        ('GET', '/total-orders?period=today', None, 200),
        ('GET', '/total-orders?period=week', None, 200),
        ('GET', '/total-orders?period=month', None, 200),
        ('GET', '/turnover', None, 200),
        ('GET', '/turnover?period=today', None, 200),
        ('GET', '/average-order-value', None, 200),
        ('GET', '/failed-submissions', None, 200),
        ('GET', '/order_status_choices/', None, 200),
        
        # Sync APIs
        ('POST', '/sync-gravity-forms', {'test': 'data'}, 200),
    ]
    
    results = []
    passed = 0
    failed = 0
    
    for method, endpoint, data, expected_status in test_cases:
        print(f"\n🧪 Testing: {method} {endpoint}")
        
        success, result = test_endpoint(method, endpoint, data, expected_status)
        
        if success:
            print(f"   ✅ PASSED - Status: {result['status_code']}, Size: {result['response_size']} bytes")
            passed += 1
        else:
            print(f"   ❌ FAILED - {result}")
            failed += 1
        
        results.append({
            'method': method,
            'endpoint': endpoint,
            'success': success,
            'result': result
        })
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📈 Success Rate: {(passed/(passed+failed)*100):.1f}%")
    
    # Failed tests details
    if failed > 0:
        print("\n❌ FAILED TESTS:")
        for test in results:
            if not test['success']:
                print(f"   {test['method']} {test['endpoint']} - {test['result']}")
    
    # API Documentation
    print("\n📚 AVAILABLE API ENDPOINTS:")
    print("Core Order Management:")
    print("  GET    /api/orders/              - List all orders")
    print("  GET    /api/orders/{id}/         - Get specific order")
    print("  POST   /api/orders/              - Create new order")
    print("  PUT    /api/orders/{id}/         - Update order")
    print("  DELETE /api/orders/{id}/         - Delete order")
    print("\nAdvanced Order APIs:")
    print("  GET/POST /api/order-details      - Search orders with filters")
    print("  GET    /api/total-orders         - Get order statistics")
    print("  GET    /api/turnover             - Get turnover data")
    print("  GET    /api/average-order-value  - Get average order value")
    print("  GET    /api/failed-submissions   - Get failed submission stats")
    print("  GET    /api/order_status_choices/ - Get status options")
    print("\nLocation APIs:")
    print("  GET    /api/locations/           - List all locations")
    print("  GET    /api/locations/{id}/      - Get specific location")
    print("\nSync APIs:")
    print("  POST   /api/sync-gravity-forms   - Sync with Gravity Forms")

if __name__ == "__main__":
    main()
