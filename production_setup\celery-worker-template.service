# Systemd service template for Celery workers
# Copy this file to /etc/systemd/system/ and customize for each location
# Example: celery-worker-barbados.service

[Unit]
Description=Celery Worker for Location Queue
After=network.target redis.service

[Service]
Type=forking
User=jgmadlga
Group=jgmadlga
WorkingDirectory=/home/<USER>/JG-Innovations--master
Environment=DJANGO_SETTINGS_MODULE=config.settings
Environment=PATH=/home/<USER>/virtualenv/JG-Innovations--master/3.9/bin:$PATH

# Customize these for each location:
# Replace LOCATION_ID with actual location UUID
# Replace LOCATION_NAME with actual location name
ExecStart=/home/<USER>/virtualenv/JG-Innovations--master/3.9/bin/celery -A config worker \
    --loglevel=info \
    --concurrency=1 \
    --queues=location.LOCATION_ID \
    --hostname=prod_worker_LOCATION_NAME@%h \
    --without-gossip \
    --without-mingle \
    --without-heartbeat \
    --detach \
    --pidfile=/var/run/celery/worker-LOCATION_NAME.pid \
    --logfile=/var/log/celery/worker-LOCATION_NAME.log

ExecStop=/bin/kill -TERM $MAINPID
ExecReload=/bin/kill -HUP $MAINPID

# Create required directories
RuntimeDirectory=celery
RuntimeDirectoryMode=755
LogsDirectory=celery
LogsDirectoryMode=755

# Restart policy
Restart=always
RestartSec=10

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/home/<USER>/JG-Innovations--master
ReadWritePaths=/var/run/celery
ReadWritePaths=/var/log/celery

[Install]
WantedBy=multi-user.target

# SETUP INSTRUCTIONS:
# 1. Copy this file to /etc/systemd/system/celery-worker-LOCATION_NAME.service
# 2. Replace LOCATION_ID and LOCATION_NAME with actual values
# 3. Create directories: sudo mkdir -p /var/run/celery /var/log/celery
# 4. Set permissions: sudo chown jgmadlga:jgmadlga /var/run/celery /var/log/celery
# 5. Enable service: sudo systemctl enable celery-worker-LOCATION_NAME.service
# 6. Start service: sudo systemctl start celery-worker-LOCATION_NAME.service
# 7. Check status: sudo systemctl status celery-worker-LOCATION_NAME.service
