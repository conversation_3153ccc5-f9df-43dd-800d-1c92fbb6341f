# Generated by Django 4.2.21 on 2025-07-14 20:20

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('queue_system', '0002_joberror_error_details'),
    ]

    operations = [
        migrations.CreateModel(
            name='JobTimeline',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_type', models.CharField(choices=[('created', 'Job Created'), ('queued', 'Queued for Processing'), ('processing_started', 'Processing Started'), ('processing_completed', 'Processing Completed'), ('failed', 'Processing Failed'), ('retry_attempted', 'Retry Attempted'), ('moved_to_review', 'Moved to Review Queue'), ('reviewed', 'Reviewed by Admin'), ('requeued', 'Requeued for Processing'), ('cancelled', 'Cancelled'), ('priority_changed', 'Priority Changed'), ('worker_assigned', 'Worker Assigned'), ('worker_released', 'Worker Released'), ('error_occurred', 'Error Occurred'), ('status_changed', 'Status Changed'), ('manual_intervention', 'Manual Intervention')], max_length=30)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('description', models.TextField(help_text='Detailed description of the event')),
                ('metadata', models.JSONField(blank=True, help_text='Additional event data (error details, previous values, etc.)', null=True)),
                ('previous_status', models.CharField(blank=True, max_length=20, null=True)),
                ('new_status', models.CharField(blank=True, max_length=20, null=True)),
                ('retry_count_at_event', models.PositiveIntegerField(blank=True, null=True)),
                ('worker_id', models.CharField(blank=True, max_length=100, null=True)),
                ('job', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='timeline_events', to='queue_system.queuedjob')),
                ('user', models.ForeignKey(blank=True, help_text='User who triggered this event (if applicable)', null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Job Timeline Event',
                'verbose_name_plural': 'Job Timeline Events',
                'ordering': ['-timestamp'],
            },
        ),
    ]
