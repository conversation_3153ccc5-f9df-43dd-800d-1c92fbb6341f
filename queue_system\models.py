from django.db import models
from django.utils import timezone

# Import the correct model names with their exact case
from orders.models import order as Order  # Alias to match our code
from locations.models import Location

class LocationQueueConfig(models.Model):
    """Configuration for location-specific queue settings"""
    location = models.OneToOneField(Location, on_delete=models.CASCADE, related_name='queue_config')
    has_time_window = models.BooleanField(default=False, 
                                         help_text="If enabled, jobs will only be processed within a specific time window before travel date")
    window_days_before_travel = models.PositiveIntegerField(null=True, blank=True,
                                                          help_text="Number of days before travel date when processing should begin")
    max_workers = models.PositiveIntegerField(default=1,
                                             help_text="Maximum number of concurrent workers for this location")
    active_workers = models.PositiveIntegerField(default=0,
                                               help_text="Current number of active workers")
    priority_level = models.PositiveIntegerField(default=1,
                                               help_text="Priority level for this location (higher numbers = higher priority)")
    auto_scale = models.BooleanField(default=True,
                                    help_text="Automatically scale workers based on queue length")
    min_workers = models.PositiveIntegerField(default=0,
                                             help_text="Minimum number of workers to keep running")
    
    def __str__(self):
        return f"{self.location.location_name} Queue Config"
    
    class Meta:
        verbose_name = "Location Queue Configuration"
        verbose_name_plural = "Location Queue Configurations"


class QueuedJob(models.Model):
    """Represents a job in the processing queue"""
    STATUS_CHOICES = [
        ('queued', 'Queued'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
        ('review', 'Under Review'),
        ('requeued', 'Requeued'),
    ]
    
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='queue_jobs')
    location = models.ForeignKey(Location, on_delete=models.CASCADE)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='queued')
    created_at = models.DateTimeField(auto_now_add=True)
    scheduled_for = models.DateTimeField(null=True, blank=True)
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    retry_count = models.PositiveIntegerField(default=0)
    max_retries = models.PositiveIntegerField(default=3)
    priority_flag = models.BooleanField(default=False)
    worker_id = models.CharField(max_length=100, null=True, blank=True)

    # Error tracking fields
    error_message = models.TextField(null=True, blank=True, help_text="Last error message")
    error_details = models.JSONField(null=True, blank=True, help_text="Detailed error information")
    failure_reason = models.CharField(max_length=200, null=True, blank=True, help_text="Categorized failure reason")

    # Review fields
    reviewed_by = models.CharField(max_length=100, null=True, blank=True, help_text="Admin who reviewed the job")
    review_notes = models.TextField(null=True, blank=True, help_text="Admin notes about the failure")
    reviewed_at = models.DateTimeField(null=True, blank=True)

    # Requeue fields
    requeue_priority = models.BooleanField(default=False, help_text="Process with high priority when requeued")
    requeue_reason = models.TextField(null=True, blank=True, help_text="Reason for requeuing")
    
    def __str__(self):
        return f"Job {self.id} - {self.order.first_name} {self.order.surname} - {self.get_status_display()}"
    
    def mark_as_processing(self, worker_id=None):
        self.status = 'processing'
        self.started_at = timezone.now()
        self.worker_id = worker_id
        self.save()
    
    def mark_as_completed(self):
        self.status = 'completed'
        self.completed_at = timezone.now()
        self.save()
    
    def mark_as_failed(self, error_message=None, error_details=None, failure_reason=None):
        """Mark job as failed and determine if it should go to review queue"""
        # Only increment retry count if we haven't exceeded max retries
        if self.retry_count < self.max_retries:
            self.retry_count += 1

        self.error_message = error_message
        self.error_details = error_details or {}
        self.failure_reason = failure_reason

        # Check if job should be moved to review queue
        if self.should_move_to_review():
            self.status = 'review'
        else:
            self.status = 'failed'

        self.completed_at = timezone.now()
        self.save()

    def mark_as_failed_final(self, error_message=None, error_details=None, failure_reason=None):
        """Mark job as failed without incrementing retry count (for final failures)"""
        self.error_message = error_message
        self.error_details = error_details or {}
        self.failure_reason = failure_reason

        # Check if job should be moved to review queue
        if self.should_move_to_review():
            self.status = 'review'
        else:
            self.status = 'failed'

        self.completed_at = timezone.now()
        self.save()

    def should_move_to_review(self):
        """Determine if job should be moved to review queue"""
        # Move to review if max retries exceeded
        if self.retry_count >= self.max_retries:
            return True

        # Move to review for specific error types
        review_error_types = [
            'missing_form_data',
            'invalid_order_data',
            'bot_configuration_error',
            'website_structure_changed',
            'authentication_failed'
        ]

        if self.failure_reason in review_error_types:
            return True

        return False

    def move_to_review(self, admin_user=None, notes=None):
        """Manually move job to review queue"""
        self.status = 'review'
        if admin_user:
            self.reviewed_by = admin_user
        if notes:
            self.review_notes = notes
        self.reviewed_at = timezone.now()
        self.save()

    def requeue_job(self, admin_user=None, priority=False, reason=None):
        """Requeue job for processing"""
        self.status = 'requeued'  # Always set to requeued when manually requeued from review
        self.retry_count = 0  # Reset retry count
        self.requeue_priority = priority
        self.requeue_reason = reason
        self.priority_flag = priority  # Set priority flag based on priority parameter
        self.reviewed_by = admin_user
        self.reviewed_at = timezone.now()
        self.started_at = None
        self.completed_at = None
        self.error_message = None
        self.error_details = None
        self.failure_reason = None
        self.save()

        # Actually schedule the job for processing
        from .tasks import process_order
        try:
            queue_name = f'location.{self.location.id}'
            process_order.apply_async(
                args=[str(self.order.id)],
                queue=queue_name,
                countdown=5  # Wait 5 seconds before processing
            )
        except Exception as e:
            # Fallback to direct processing
            import logging
            logger = logging.getLogger(__name__)
            logger.info(f"Redis queue failed for requeue, using background processing: {str(e)}")

            from threading import Thread
            def process_in_background():
                import time
                time.sleep(5)  # Wait 5 seconds
                process_order(str(self.order.id))

            thread = Thread(target=process_in_background)
            thread.daemon = True
            thread.start()

    def requeue(self):
        """Legacy method - use requeue_job instead"""
        self.status = 'requeued'
        self.retry_count += 1
        self.save()
    
    def order_link(self):
        return f"{self.order.first_name} {self.order.surname}"
    order_link.short_description = "Customer"

    class Meta:
        ordering = ['-priority_flag', 'created_at']
        verbose_name = "Queued Job"
        verbose_name_plural = "Queued Jobs"


class JobError(models.Model):
    """Records errors that occur during job processing"""
    job = models.ForeignKey(QueuedJob, on_delete=models.CASCADE, related_name='errors')
    occurred_at = models.DateTimeField(auto_now_add=True)
    error_message = models.TextField()
    error_trace = models.TextField(blank=True)
    error_details = models.JSONField(blank=True, null=True, help_text="Detailed error information including context and metadata")
    
    def __str__(self):
        return f"Error for Job {self.job.id} at {self.occurred_at}"

    class Meta:
        ordering = ['-occurred_at']
        verbose_name = "Job Error"
        verbose_name_plural = "Job Errors"




