from django.db import models
from django.utils import timezone

# Import the correct model names with their exact case
from orders.models import order as Order  # Alias to match our code
from locations.models import Location

class LocationQueueConfig(models.Model):
    """Configuration for location-specific queue settings"""
    location = models.OneToOneField(Location, on_delete=models.CASCADE, related_name='queue_config')
    has_time_window = models.BooleanField(default=False, 
                                         help_text="If enabled, jobs will only be processed within a specific time window before travel date")
    window_days_before_travel = models.PositiveIntegerField(null=True, blank=True,
                                                          help_text="Number of days before travel date when processing should begin")
    max_workers = models.PositiveIntegerField(default=1,
                                             help_text="Maximum number of concurrent workers for this location")
    active_workers = models.PositiveIntegerField(default=0,
                                               help_text="Current number of active workers")
    priority_level = models.PositiveIntegerField(default=1,
                                               help_text="Priority level for this location (higher numbers = higher priority)")
    auto_scale = models.BooleanField(default=True,
                                    help_text="Automatically scale workers based on queue length")
    min_workers = models.PositiveIntegerField(default=0,
                                             help_text="Minimum number of workers to keep running")
    
    def __str__(self):
        return f"{self.location.location_name} Queue Config"
    
    class Meta:
        verbose_name = "Location Queue Configuration"
        verbose_name_plural = "Location Queue Configurations"


class QueuedJob(models.Model):
    """Represents a job in the processing queue"""
    STATUS_CHOICES = [
        ('queued', 'Queued'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
        ('review', 'Under Review'),
        ('requeued', 'Requeued'),
    ]
    
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='queue_jobs')
    location = models.ForeignKey(Location, on_delete=models.CASCADE)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='queued')
    created_at = models.DateTimeField(auto_now_add=True)
    scheduled_for = models.DateTimeField(null=True, blank=True)
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    retry_count = models.PositiveIntegerField(default=0)
    max_retries = models.PositiveIntegerField(default=3)
    priority_flag = models.BooleanField(default=False)
    worker_id = models.CharField(max_length=100, null=True, blank=True)

    # Error tracking fields
    error_message = models.TextField(null=True, blank=True, help_text="Last error message")
    error_details = models.JSONField(null=True, blank=True, help_text="Detailed error information")
    failure_reason = models.CharField(max_length=200, null=True, blank=True, help_text="Categorized failure reason")

    # Review fields
    reviewed_by = models.CharField(max_length=100, null=True, blank=True, help_text="Admin who reviewed the job")
    review_notes = models.TextField(null=True, blank=True, help_text="Admin notes about the failure")
    reviewed_at = models.DateTimeField(null=True, blank=True)

    # Requeue fields
    requeue_priority = models.BooleanField(default=False, help_text="Process with high priority when requeued")
    requeue_reason = models.TextField(null=True, blank=True, help_text="Reason for requeuing")
    
    def __str__(self):
        return f"Job {self.id} - {self.order.first_name} {self.order.surname} - {self.get_status_display()}"

    def _create_timeline_event(self, event_type, description, user=None, metadata=None,
                              previous_status=None, new_status=None):
        """Helper method to create timeline events"""
        try:
            # Use apps.get_model to avoid circular imports
            from django.apps import apps
            JobTimeline = apps.get_model('queue_system', 'JobTimeline')

            JobTimeline.objects.create(
                job=self,
                event_type=event_type,
                description=description,
                user=user,
                metadata=metadata or {},
                previous_status=previous_status,
                new_status=new_status,
                retry_count_at_event=self.retry_count,
                worker_id=self.worker_id
            )
        except Exception as e:
            # Don't let timeline creation failures break the main operation
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Failed to create timeline event for job {self.id}: {str(e)}")

    def mark_as_processing(self, worker_id=None):
        previous_status = self.status
        self.status = 'processing'
        self.started_at = timezone.now()
        self.worker_id = worker_id
        self.save()

        # Create timeline events
        self._create_timeline_event(
            event_type='processing_started',
            description=f"Processing started by worker {worker_id or 'unknown'}",
            previous_status=previous_status,
            new_status='processing',
            metadata={'worker_id': worker_id}
        )

        if worker_id:
            self._create_timeline_event(
                event_type='worker_assigned',
                description=f"Worker {worker_id} assigned to job",
                metadata={'worker_id': worker_id}
            )
    
    def mark_as_completed(self):
        previous_status = self.status
        self.status = 'completed'
        self.completed_at = timezone.now()
        self.save()

        # Create timeline event
        self._create_timeline_event(
            event_type='processing_completed',
            description="Job completed successfully",
            previous_status=previous_status,
            new_status='completed',
            metadata={'completed_at': self.completed_at.isoformat()}
        )
    
    def mark_as_failed(self, error_message=None, error_details=None, failure_reason=None):
        """Mark job as failed and determine if it should go to review queue"""
        previous_status = self.status
        previous_retry_count = self.retry_count

        # Only increment retry count if we haven't exceeded max retries
        if self.retry_count < self.max_retries:
            self.retry_count += 1

        self.error_message = error_message
        self.error_details = error_details or {}
        self.failure_reason = failure_reason

        # Check if job should be moved to review queue
        will_move_to_review = self.should_move_to_review()
        if will_move_to_review:
            self.status = 'review'
        else:
            self.status = 'failed'

        self.completed_at = timezone.now()
        self.save()

        # Create timeline events
        if self.retry_count > previous_retry_count:
            self._create_timeline_event(
                event_type='retry_attempted',
                description=f"Retry attempt {self.retry_count} of {self.max_retries}",
                metadata={
                    'retry_count': self.retry_count,
                    'max_retries': self.max_retries,
                    'error_message': error_message,
                    'failure_reason': failure_reason
                }
            )

        self._create_timeline_event(
            event_type='failed',
            description=f"Job failed: {error_message or failure_reason or 'Unknown error'}",
            previous_status=previous_status,
            new_status=self.status,
            metadata={
                'error_message': error_message,
                'error_details': error_details,
                'failure_reason': failure_reason,
                'retry_count': self.retry_count
            }
        )

        if will_move_to_review:
            self._create_timeline_event(
                event_type='moved_to_review',
                description=f"Moved to review queue after {self.retry_count} attempts",
                metadata={
                    'reason': 'max_retries_exceeded' if self.retry_count >= self.max_retries else 'error_type_requires_review',
                    'retry_count': self.retry_count
                }
            )

    def mark_as_failed_final(self, error_message=None, error_details=None, failure_reason=None):
        """Mark job as failed without incrementing retry count (for final failures)"""
        self.error_message = error_message
        self.error_details = error_details or {}
        self.failure_reason = failure_reason

        # Check if job should be moved to review queue
        if self.should_move_to_review():
            self.status = 'review'
        else:
            self.status = 'failed'

        self.completed_at = timezone.now()
        self.save()

    def should_move_to_review(self):
        """Determine if job should be moved to review queue"""
        # Move to review if max retries exceeded
        if self.retry_count >= self.max_retries:
            return True

        # Move to review for specific error types
        review_error_types = [
            'missing_form_data',
            'invalid_order_data',
            'bot_configuration_error',
            'website_structure_changed',
            'authentication_failed'
        ]

        if self.failure_reason in review_error_types:
            return True

        return False

    def move_to_review(self, admin_user=None, notes=None):
        """Manually move job to review queue"""
        previous_status = self.status
        self.status = 'review'
        if admin_user:
            self.reviewed_by = admin_user
        if notes:
            self.review_notes = notes
        self.reviewed_at = timezone.now()
        self.save()

        # Create timeline event
        self._create_timeline_event(
            event_type='moved_to_review',
            description=f"Manually moved to review queue by {admin_user.username if admin_user else 'system'}",
            user=admin_user,
            previous_status=previous_status,
            new_status='review',
            metadata={
                'notes': notes,
                'manual_action': True
            }
        )

    def requeue_job(self, admin_user=None, priority=False, reason=None):
        """Requeue job for processing"""
        previous_status = self.status
        previous_retry_count = self.retry_count

        self.status = 'requeued'  # Always set to requeued when manually requeued from review
        self.retry_count = 0  # Reset retry count
        self.requeue_priority = priority
        self.requeue_reason = reason
        self.priority_flag = priority  # Set priority flag based on priority parameter
        self.reviewed_by = admin_user
        self.reviewed_at = timezone.now()
        self.started_at = None
        self.completed_at = None
        self.error_message = None
        self.error_details = None
        self.failure_reason = None
        self.save()

        # Create timeline events
        if admin_user:
            self._create_timeline_event(
                event_type='reviewed',
                description=f"Job reviewed by {admin_user.username}",
                user=admin_user,
                metadata={
                    'review_action': 'requeue',
                    'reason': reason,
                    'priority': priority
                }
            )

        self._create_timeline_event(
            event_type='requeued',
            description=f"Job requeued for processing{' with high priority' if priority else ''}",
            user=admin_user,
            previous_status=previous_status,
            new_status='requeued',
            metadata={
                'reason': reason,
                'priority': priority,
                'previous_retry_count': previous_retry_count,
                'reset_retry_count': True
            }
        )

        if priority:
            self._create_timeline_event(
                event_type='priority_changed',
                description="Job marked as high priority",
                user=admin_user,
                metadata={'priority_flag': True}
            )

        # Actually schedule the job for processing
        from .tasks import process_order
        try:
            queue_name = f'location.{self.location.id}'
            process_order.apply_async(
                args=[str(self.order.id)],
                queue=queue_name,
                countdown=5  # Wait 5 seconds before processing
            )
        except Exception as e:
            # Fallback to direct processing
            import logging
            logger = logging.getLogger(__name__)
            logger.info(f"Redis queue failed for requeue, using background processing: {str(e)}")

            from threading import Thread
            def process_in_background():
                import time
                time.sleep(5)  # Wait 5 seconds
                process_order(str(self.order.id))

            thread = Thread(target=process_in_background)
            thread.daemon = True
            thread.start()

    def requeue(self):
        """Legacy method - use requeue_job instead"""
        self.status = 'requeued'
        self.retry_count += 1
        self.save()
    
    def order_link(self):
        return f"{self.order.first_name} {self.order.surname}"
    order_link.short_description = "Customer"

    class Meta:
        ordering = ['-priority_flag', 'created_at']
        verbose_name = "Queued Job"
        verbose_name_plural = "Queued Jobs"


class JobError(models.Model):
    """Records errors that occur during job processing"""
    job = models.ForeignKey(QueuedJob, on_delete=models.CASCADE, related_name='errors')
    occurred_at = models.DateTimeField(auto_now_add=True)
    error_message = models.TextField()
    error_trace = models.TextField(blank=True)
    error_details = models.JSONField(blank=True, null=True, help_text="Detailed error information including context and metadata")

    def __str__(self):
        return f"Error for Job {self.job.id} at {self.occurred_at}"

    class Meta:
        ordering = ['-occurred_at']
        verbose_name = "Job Error"
        verbose_name_plural = "Job Errors"


class JobTimeline(models.Model):
    """Records all events in a job's lifecycle for comprehensive timeline tracking"""

    EVENT_TYPES = [
        ('created', 'Job Created'),
        ('queued', 'Queued for Processing'),
        ('processing_started', 'Processing Started'),
        ('processing_completed', 'Processing Completed'),
        ('failed', 'Processing Failed'),
        ('retry_attempted', 'Retry Attempted'),
        ('moved_to_review', 'Moved to Review Queue'),
        ('reviewed', 'Reviewed by Admin'),
        ('requeued', 'Requeued for Processing'),
        ('cancelled', 'Cancelled'),
        ('priority_changed', 'Priority Changed'),
        ('worker_assigned', 'Worker Assigned'),
        ('worker_released', 'Worker Released'),
        ('error_occurred', 'Error Occurred'),
        ('status_changed', 'Status Changed'),
        ('manual_intervention', 'Manual Intervention'),
    ]

    job = models.ForeignKey(QueuedJob, on_delete=models.CASCADE, related_name='timeline_events')
    event_type = models.CharField(max_length=30, choices=EVENT_TYPES)
    timestamp = models.DateTimeField(auto_now_add=True)
    description = models.TextField(help_text="Detailed description of the event")
    user = models.ForeignKey('auth.User', on_delete=models.SET_NULL, null=True, blank=True,
                           help_text="User who triggered this event (if applicable)")
    metadata = models.JSONField(blank=True, null=True,
                               help_text="Additional event data (error details, previous values, etc.)")

    # Status tracking
    previous_status = models.CharField(max_length=20, blank=True, null=True)
    new_status = models.CharField(max_length=20, blank=True, null=True)

    # Retry tracking
    retry_count_at_event = models.PositiveIntegerField(null=True, blank=True)

    # Worker tracking
    worker_id = models.CharField(max_length=100, blank=True, null=True)

    def __str__(self):
        return f"Job {self.job.id} - {self.get_event_type_display()} at {self.timestamp}"

    def get_event_icon(self):
        """Return an appropriate icon for the event type"""
        icons = {
            'created': '🆕',
            'queued': '⏳',
            'processing_started': '▶️',
            'processing_completed': '✅',
            'failed': '❌',
            'retry_attempted': '🔄',
            'moved_to_review': '👀',
            'reviewed': '🔍',
            'requeued': '🔁',
            'cancelled': '🚫',
            'priority_changed': '⚡',
            'worker_assigned': '👷',
            'worker_released': '👋',
            'error_occurred': '⚠️',
            'status_changed': '📝',
            'manual_intervention': '👤',
        }
        return icons.get(self.event_type, '📋')

    def get_event_color(self):
        """Return appropriate color class for the event type"""
        colors = {
            'created': 'text-blue-600',
            'queued': 'text-yellow-600',
            'processing_started': 'text-blue-500',
            'processing_completed': 'text-green-600',
            'failed': 'text-red-600',
            'retry_attempted': 'text-orange-600',
            'moved_to_review': 'text-purple-600',
            'reviewed': 'text-indigo-600',
            'requeued': 'text-cyan-600',
            'cancelled': 'text-gray-600',
            'priority_changed': 'text-yellow-500',
            'worker_assigned': 'text-green-500',
            'worker_released': 'text-gray-500',
            'error_occurred': 'text-red-500',
            'status_changed': 'text-blue-400',
            'manual_intervention': 'text-purple-500',
        }
        return colors.get(self.event_type, 'text-gray-600')

    class Meta:
        ordering = ['-timestamp']
        verbose_name = "Job Timeline Event"
        verbose_name_plural = "Job Timeline Events"




